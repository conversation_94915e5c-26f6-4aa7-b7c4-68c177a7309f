#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
36氪新闻爬虫
爬取36氪网站的最新新闻文章
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import csv
from datetime import datetime
import re
import os

class Kr36Crawler:
    def __init__(self):
        self.base_url = "https://36kr.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return None
    
    def parse_news_list(self, html_content):
        """解析新闻列表"""
        soup = BeautifulSoup(html_content, 'html.parser')
        news_list = []
        
        # 查找新闻流容器
        flow_container = soup.find('div', class_='kr-home-flow')
        if not flow_container:
            print("未找到新闻流容器")
            return news_list
        
        # 查找所有新闻项
        news_items = flow_container.find_all('div', class_='kr-flow-article-item')
        
        for item in news_items:
            try:
                news_info = self.extract_news_info(item)
                if news_info:
                    news_list.append(news_info)
            except Exception as e:
                print(f"解析新闻项失败: {e}")
                continue
        
        return news_list
    
    def extract_news_info(self, item):
        """提取单个新闻信息"""
        news_info = {}
        
        # 提取标题和链接
        title_element = item.find('a', class_='article-item-title')
        if title_element:
            news_info['title'] = title_element.get_text(strip=True)
            news_info['url'] = self.base_url + title_element.get('href', '')
        else:
            return None
        
        # 提取描述
        desc_element = item.find('a', class_='article-item-description')
        if desc_element:
            news_info['description'] = desc_element.get_text(strip=True)
        
        # 提取分类
        channel_element = item.find('a', class_='article-item-channel')
        if channel_element:
            news_info['category'] = channel_element.get_text(strip=True)
        
        # 提取作者
        author_element = item.find('a', class_='kr-flow-bar-author')
        if author_element:
            news_info['author'] = author_element.get_text(strip=True)
        
        # 提取时间
        time_element = item.find('span', class_='kr-flow-bar-time')
        if time_element:
            time_text = time_element.get_text(strip=True)
            # 移除时间图标
            time_text = re.sub(r'^\s*', '', time_text)
            news_info['publish_time'] = time_text
        
        # 提取图片
        img_element = item.find('img')
        if img_element:
            news_info['image_url'] = img_element.get('src', '')
        
        # 提取主题
        motif_element = item.find('span', class_='kr-flow-bar-motif')
        if motif_element:
            motif_link = motif_element.find('a')
            if motif_link:
                news_info['topic'] = motif_link.get_text(strip=True)
        
        # 添加爬取时间
        news_info['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return news_info
    
    def get_article_content(self, article_url):
        """获取文章详细内容"""
        html_content = self.get_page_content(article_url)
        if not html_content:
            return None
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找文章内容容器
        content_container = soup.find('div', class_='article-content')
        if not content_container:
            # 尝试其他可能的容器
            content_container = soup.find('div', class_='kr-rich-text-wrapper')
        
        if content_container:
            # 提取纯文本内容
            content = content_container.get_text(strip=True, separator='\n')
            return content
        
        return None
    
    def save_to_json(self, news_list, filename='36kr_news.json'):
        """保存为JSON格式"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(news_list, f, ensure_ascii=False, indent=2)
        print(f"已保存 {len(news_list)} 条新闻到 {filename}")
    
    def save_to_csv(self, news_list, filename='36kr_news.csv'):
        """保存为CSV格式"""
        if not news_list:
            print("没有数据可保存")
            return
        
        fieldnames = ['title', 'description', 'category', 'author', 'publish_time', 
                     'topic', 'url', 'image_url', 'crawl_time']
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for news in news_list:
                writer.writerow(news)
        print(f"已保存 {len(news_list)} 条新闻到 {filename}")
    
    def crawl_news(self, save_format='both', get_content=False):
        """爬取新闻主函数"""
        print("开始爬取36氪新闻...")
        
        # 获取首页内容
        html_content = self.get_page_content(self.base_url)
        if not html_content:
            print("无法获取首页内容")
            return []
        
        # 解析新闻列表
        news_list = self.parse_news_list(html_content)
        print(f"共找到 {len(news_list)} 条新闻")
        
        # 如果需要获取详细内容
        if get_content:
            print("正在获取文章详细内容...")
            for i, news in enumerate(news_list):
                print(f"正在处理第 {i+1}/{len(news_list)} 篇文章...")
                content = self.get_article_content(news['url'])
                if content:
                    news['content'] = content
                time.sleep(1)  # 避免请求过于频繁
        
        # 保存数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if save_format in ['json', 'both']:
            self.save_to_json(news_list, f'36kr_news_{timestamp}.json')
        
        if save_format in ['csv', 'both']:
            self.save_to_csv(news_list, f'36kr_news_{timestamp}.csv')
        
        return news_list

def main():
    """主函数"""
    crawler = Kr36Crawler()
    
    print("36氪新闻爬虫")
    print("=" * 50)
    
    # 爬取新闻
    news_list = crawler.crawl_news(save_format='both', get_content=False)
    
    if news_list:
        print("\n爬取完成！")
        print(f"共获取 {len(news_list)} 条新闻")
        
        # 显示前5条新闻标题
        print("\n最新新闻预览:")
        print("-" * 50)
        for i, news in enumerate(news_list[:5]):
            print(f"{i+1}. {news.get('title', 'N/A')}")
            print(f"   分类: {news.get('category', 'N/A')} | 作者: {news.get('author', 'N/A')} | 时间: {news.get('publish_time', 'N/A')}")
            print(f"   描述: {news.get('description', 'N/A')[:100]}...")
            print()
    else:
        print("未获取到任何新闻")

if __name__ == "__main__":
    main()
