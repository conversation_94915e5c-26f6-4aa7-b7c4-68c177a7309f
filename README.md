# 36氪新闻爬虫

这是一个用于爬取36氪网站新闻的Python爬虫工具集，包含三个不同版本的爬虫，满足不同的使用需求。

## 功能特点

- 🚀 **多版本支持**: 提供基础版、简化版和高级版三种爬虫
- 📰 **全面数据**: 获取标题、描述、分类、作者、发布时间、主题等信息
- 💾 **多格式保存**: 支持JSON和CSV两种格式保存数据
- 🔄 **智能解析**: 自动解析36氪网站结构，提取新闻信息
- 📖 **内容获取**: 高级版支持获取文章详细内容
- ⚡ **请求控制**: 内置请求频率控制，避免对服务器造成压力

## 文件说明

### 爬虫文件

1. **`36kr_crawler.py`** - 基础版爬虫
   - 功能完整的新闻爬虫
   - 支持获取新闻列表和基本信息
   - 可选择是否获取文章详细内容

2. **`simple_36kr_crawler.py`** - 简化版爬虫
   - 代码简洁，易于理解和修改
   - 快速获取新闻列表
   - 适合初学者学习使用

3. **`advanced_36kr_crawler.py`** - 高级版爬虫
   - 功能最全面的版本
   - 支持获取文章详细内容
   - 包含更多数据字段和统计功能
   - 交互式操作界面

### 配置文件

- **`requirements.txt`** - Python依赖包列表
- **`README.md`** - 使用说明文档

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 基础版爬虫

```bash
python 36kr_crawler.py
```

特点：
- 自动爬取36氪首页新闻
- 保存为JSON和CSV格式
- 可选择是否获取文章详细内容

### 2. 简化版爬虫

```bash
python simple_36kr_crawler.py
```

特点：
- 代码简洁明了
- 快速获取前10条新闻
- 适合快速查看最新资讯

### 3. 高级版爬虫

```bash
python advanced_36kr_crawler.py
```

特点：
- 交互式界面，可自定义爬取数量
- 获取文章详细内容
- 提供详细的统计信息
- 支持更多数据字段

## 输出数据格式

### JSON格式示例

```json
[
  {
    "title": "文章标题",
    "url": "https://36kr.com/p/xxxxxxxxx",
    "description": "文章描述",
    "category": "科技",
    "author": "作者名称",
    "publish_time": "发布时间",
    "topic": "主题标签",
    "image_url": "图片链接",
    "crawl_time": "2025-07-07 15:26:11"
  }
]
```

### CSV格式字段

| 字段名 | 说明 |
|--------|------|
| title | 文章标题 |
| description | 文章描述 |
| category | 文章分类 |
| author | 作者 |
| publish_time | 发布时间 |
| topic | 主题标签 |
| url | 文章链接 |
| image_url | 图片链接 |
| crawl_time | 爬取时间 |

### 高级版额外字段

| 字段名 | 说明 |
|--------|------|
| full_title | 完整标题 |
| subtitle | 副标题 |
| content | 文章内容 |
| content_length | 内容长度 |
| tags | 标签列表 |
| detailed_time | 详细时间 |

## 注意事项

1. **请求频率**: 爬虫内置了请求延迟，避免对36氪服务器造成过大压力
2. **网站结构**: 如果36氪网站结构发生变化，可能需要更新解析代码
3. **使用规范**: 请遵守网站的robots.txt协议和使用条款
4. **数据用途**: 爬取的数据仅供学习和研究使用

## 自定义配置

### 修改请求头

在爬虫代码中找到`headers`字典，可以修改User-Agent等信息：

```python
headers = {
    'User-Agent': '你的User-Agent',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    # 其他头信息...
}
```

### 调整爬取数量

在`simple_36kr_crawler.py`中修改：

```python
for i, item in enumerate(news_items[:10]):  # 修改数字10为你想要的数量
```

### 修改保存格式

可以在保存函数中添加其他格式支持，如Excel、数据库等。

## 常见问题

### Q: 爬虫运行失败怎么办？
A: 检查网络连接，确认36氪网站可以正常访问，检查依赖包是否正确安装。

### Q: 获取不到数据怎么办？
A: 36氪网站结构可能发生了变化，需要更新解析代码中的CSS选择器。

### Q: 如何获取更多文章？
A: 使用高级版爬虫，在运行时输入想要的文章数量。

### Q: 可以爬取特定分类的文章吗？
A: 当前版本爬取首页文章，如需特定分类，需要修改代码中的URL和解析逻辑。

## 版本历史

- v1.0 - 基础功能实现
- v1.1 - 添加简化版爬虫
- v1.2 - 添加高级版爬虫，支持详细内容获取

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。
