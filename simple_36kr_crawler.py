#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版36氪新闻爬虫
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
from datetime import datetime
import re

def get_36kr_news():
    """获取36氪新闻"""
    url = "https://36kr.com"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        print("正在访问36氪网站...")
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        print("正在解析页面内容...")
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找新闻项
        news_items = soup.find_all('div', class_='kr-flow-article-item')
        print(f"找到 {len(news_items)} 个新闻项")
        
        news_list = []
        
        for i, item in enumerate(news_items[:10]):  # 只取前10条
            try:
                news_info = {}
                
                # 标题和链接
                title_element = item.find('a', class_='article-item-title')
                if title_element:
                    news_info['title'] = title_element.get_text(strip=True)
                    news_info['url'] = "https://36kr.com" + title_element.get('href', '')
                
                # 描述
                desc_element = item.find('a', class_='article-item-description')
                if desc_element:
                    news_info['description'] = desc_element.get_text(strip=True)
                
                # 分类
                channel_element = item.find('a', class_='article-item-channel')
                if channel_element:
                    news_info['category'] = channel_element.get_text(strip=True)
                
                # 作者
                author_element = item.find('a', class_='kr-flow-bar-author')
                if author_element:
                    news_info['author'] = author_element.get_text(strip=True)
                
                # 时间
                time_element = item.find('span', class_='kr-flow-bar-time')
                if time_element:
                    news_info['publish_time'] = time_element.get_text(strip=True)
                
                # 主题
                motif_element = item.find('span', class_='kr-flow-bar-motif')
                if motif_element:
                    motif_link = motif_element.find('a')
                    if motif_link:
                        news_info['topic'] = motif_link.get_text(strip=True)
                
                news_info['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                if news_info.get('title'):  # 只有有标题的才添加
                    news_list.append(news_info)
                    print(f"成功解析第 {len(news_list)} 条新闻: {news_info['title'][:50]}...")
                
            except Exception as e:
                print(f"解析第 {i+1} 个新闻项时出错: {e}")
                continue
        
        return news_list
        
    except Exception as e:
        print(f"爬取失败: {e}")
        return []

def save_news(news_list):
    """保存新闻数据"""
    if not news_list:
        print("没有数据可保存")
        return
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 保存为JSON
    json_filename = f'36kr_news_{timestamp}.json'
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(news_list, f, ensure_ascii=False, indent=2)
    print(f"已保存到 {json_filename}")
    
    # 保存为CSV
    csv_filename = f'36kr_news_{timestamp}.csv'
    fieldnames = ['title', 'description', 'category', 'author', 'publish_time', 'topic', 'url', 'crawl_time']
    
    with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        for news in news_list:
            writer.writerow(news)
    print(f"已保存到 {csv_filename}")

def main():
    print("36氪新闻爬虫")
    print("=" * 50)
    
    # 爬取新闻
    news_list = get_36kr_news()
    
    if news_list:
        print(f"\n成功获取 {len(news_list)} 条新闻")
        
        # 保存数据
        save_news(news_list)
        
        # 显示前5条新闻
        print("\n最新新闻预览:")
        print("-" * 50)
        for i, news in enumerate(news_list[:5]):
            print(f"{i+1}. {news.get('title', 'N/A')}")
            print(f"   分类: {news.get('category', 'N/A')} | 作者: {news.get('author', 'N/A')}")
            print(f"   时间: {news.get('publish_time', 'N/A')}")
            print(f"   描述: {news.get('description', 'N/A')[:80]}...")
            print(f"   链接: {news.get('url', 'N/A')}")
            print()
    else:
        print("未获取到任何新闻")

if __name__ == "__main__":
    main()
