#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级版36氪新闻爬虫 - 包含文章详细内容获取
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
import time
from datetime import datetime
import re
import os

class Advanced36KrCrawler:
    def __init__(self):
        self.base_url = "https://36kr.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://36kr.com/',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            print(f"正在访问: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except requests.RequestException as e:
            print(f"请求失败 {url}: {e}")
            return None
    
    def get_news_list(self):
        """获取新闻列表"""
        html_content = self.get_page_content(self.base_url)
        if not html_content:
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        news_list = []
        
        # 查找新闻项
        news_items = soup.find_all('div', class_='kr-flow-article-item')
        print(f"找到 {len(news_items)} 个新闻项")
        
        for item in news_items:
            try:
                news_info = self.extract_basic_info(item)
                if news_info:
                    news_list.append(news_info)
            except Exception as e:
                print(f"解析新闻项失败: {e}")
                continue
        
        return news_list
    
    def extract_basic_info(self, item):
        """提取基本新闻信息"""
        news_info = {}
        
        # 标题和链接
        title_element = item.find('a', class_='article-item-title')
        if title_element:
            news_info['title'] = title_element.get_text(strip=True)
            news_info['url'] = self.base_url + title_element.get('href', '')
        else:
            return None
        
        # 描述
        desc_element = item.find('a', class_='article-item-description')
        if desc_element:
            news_info['description'] = desc_element.get_text(strip=True)
        
        # 分类
        channel_element = item.find('a', class_='article-item-channel')
        if channel_element:
            news_info['category'] = channel_element.get_text(strip=True)
        
        # 作者
        author_element = item.find('a', class_='kr-flow-bar-author')
        if author_element:
            news_info['author'] = author_element.get_text(strip=True)
        
        # 时间
        time_element = item.find('span', class_='kr-flow-bar-time')
        if time_element:
            news_info['publish_time'] = time_element.get_text(strip=True)
        
        # 主题
        motif_element = item.find('span', class_='kr-flow-bar-motif')
        if motif_element:
            motif_link = motif_element.find('a')
            if motif_link:
                news_info['topic'] = motif_link.get_text(strip=True)
        
        # 图片
        img_element = item.find('img')
        if img_element:
            news_info['image_url'] = img_element.get('src', '')
        
        news_info['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return news_info
    
    def get_article_content(self, article_url):
        """获取文章详细内容"""
        html_content = self.get_page_content(article_url)
        if not html_content:
            return None
        
        soup = BeautifulSoup(html_content, 'html.parser')
        article_data = {}
        
        # 文章标题
        title_element = soup.find('h1', class_='article-title')
        if title_element:
            article_data['full_title'] = title_element.get_text(strip=True)
        
        # 文章副标题
        subtitle_element = soup.find('div', class_='article-subtitle')
        if subtitle_element:
            article_data['subtitle'] = subtitle_element.get_text(strip=True)
        
        # 文章内容
        content_selectors = [
            'div.article-content',
            'div.kr-rich-text-wrapper',
            'div.common-width.margin-bottom-20',
            'div[data-v-7f856186]'
        ]
        
        content = None
        for selector in content_selectors:
            content_element = soup.select_one(selector)
            if content_element:
                # 移除广告和无关元素
                for ad in content_element.find_all(['script', 'style', 'iframe']):
                    ad.decompose()
                
                # 提取纯文本
                content = content_element.get_text(strip=True, separator='\n')
                if content and len(content) > 100:  # 确保内容足够长
                    break
        
        if content:
            article_data['content'] = content
            article_data['content_length'] = len(content)
        
        # 文章标签
        tags = []
        tag_elements = soup.find_all('a', href=re.compile(r'/motif/'))
        for tag_elem in tag_elements:
            tag_text = tag_elem.get_text(strip=True)
            if tag_text and tag_text not in tags:
                tags.append(tag_text)
        
        if tags:
            article_data['tags'] = tags
        
        # 发布时间（更详细）
        time_element = soup.find('span', class_='time')
        if time_element:
            article_data['detailed_time'] = time_element.get_text(strip=True)
        
        return article_data
    
    def crawl_with_content(self, max_articles=5):
        """爬取新闻并获取详细内容"""
        print("开始爬取36氪新闻（包含详细内容）...")
        
        # 获取新闻列表
        news_list = self.get_news_list()
        if not news_list:
            print("未获取到新闻列表")
            return []
        
        print(f"共找到 {len(news_list)} 条新闻，将获取前 {max_articles} 条的详细内容")
        
        # 获取详细内容
        detailed_articles = []
        for i, news in enumerate(news_list[:max_articles]):
            print(f"\n正在处理第 {i+1}/{min(max_articles, len(news_list))} 篇文章...")
            print(f"标题: {news['title']}")
            
            # 获取文章详细内容
            article_content = self.get_article_content(news['url'])
            
            # 合并基本信息和详细内容
            if article_content:
                news.update(article_content)
                print(f"成功获取内容，长度: {article_content.get('content_length', 0)} 字符")
            else:
                print("获取详细内容失败")
            
            detailed_articles.append(news)
            
            # 避免请求过于频繁
            if i < max_articles - 1:
                time.sleep(2)
        
        return detailed_articles
    
    def save_detailed_data(self, articles, format_type='both'):
        """保存详细数据"""
        if not articles:
            print("没有数据可保存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if format_type in ['json', 'both']:
            json_filename = f'36kr_detailed_{timestamp}.json'
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(articles, f, ensure_ascii=False, indent=2)
            print(f"已保存详细数据到 {json_filename}")
        
        if format_type in ['csv', 'both']:
            csv_filename = f'36kr_detailed_{timestamp}.csv'
            
            # 准备CSV字段
            fieldnames = ['title', 'full_title', 'subtitle', 'description', 'category', 
                         'author', 'publish_time', 'detailed_time', 'topic', 'tags',
                         'url', 'image_url', 'content_length', 'content', 'crawl_time']
            
            with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for article in articles:
                    # 处理标签列表
                    row_data = article.copy()
                    if 'tags' in row_data and isinstance(row_data['tags'], list):
                        row_data['tags'] = ', '.join(row_data['tags'])
                    
                    writer.writerow(row_data)
            
            print(f"已保存详细数据到 {csv_filename}")

def main():
    print("高级版36氪新闻爬虫")
    print("=" * 60)
    
    crawler = Advanced36KrCrawler()
    
    # 询问用户要爬取多少篇文章
    try:
        max_articles = int(input("请输入要爬取的文章数量（默认5篇）: ") or "5")
    except ValueError:
        max_articles = 5
    
    # 爬取新闻
    articles = crawler.crawl_with_content(max_articles)
    
    if articles:
        print(f"\n爬取完成！共获取 {len(articles)} 篇文章的详细内容")
        
        # 保存数据
        crawler.save_detailed_data(articles)
        
        # 显示统计信息
        print("\n文章统计:")
        print("-" * 40)
        total_content_length = sum(article.get('content_length', 0) for article in articles)
        print(f"总内容长度: {total_content_length:,} 字符")
        
        categories = {}
        for article in articles:
            cat = article.get('category', '未分类')
            categories[cat] = categories.get(cat, 0) + 1
        
        print("分类统计:")
        for cat, count in categories.items():
            print(f"  {cat}: {count} 篇")
        
        print("\n文章预览:")
        print("-" * 40)
        for i, article in enumerate(articles[:3]):
            print(f"{i+1}. {article.get('title', 'N/A')}")
            print(f"   内容长度: {article.get('content_length', 0)} 字符")
            if article.get('content'):
                preview = article['content'][:100].replace('\n', ' ')
                print(f"   内容预览: {preview}...")
            print()
    else:
        print("未获取到任何文章")

if __name__ == "__main__":
    main()
